/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-bg: #1a1a1a;
    --secondary-bg: #2a2a2a;
    --text-primary: #f0f0f0;
    --text-secondary: #b0b0b0;
    --accent-color: #007bff;
    --accent-hover: #0056b3;
    --border-color: #333;
    --shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    --transition: all 0.3s ease;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: 'Inter', sans-serif;
    background-color: var(--primary-bg);
    color: var(--text-primary);
    line-height: 1.6;
    overflow-x: hidden;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Navigation */
.navbar {
    position: fixed;
    top: 0;
    width: 100%;
    background: rgba(26, 26, 26, 0.95);
    backdrop-filter: blur(10px);
    z-index: 1000;
    padding: 1rem 0;
    transition: var(--transition);
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-logo a {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--accent-color);
    text-decoration: none;
}

.nav-menu {
    display: flex;
    gap: 2rem;
}

.nav-link {
    color: var(--text-primary);
    text-decoration: none;
    font-weight: 500;
    transition: var(--transition);
    position: relative;
}

.nav-link:hover {
    color: var(--accent-color);
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--accent-color);
    transition: var(--transition);
}

.nav-link:hover::after {
    width: 100%;
}

.nav-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.bar {
    width: 25px;
    height: 3px;
    background: var(--text-primary);
    margin: 3px 0;
    transition: var(--transition);
}

/* Hero Section */
.hero {
    height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    background: linear-gradient(135deg, var(--primary-bg) 0%, var(--secondary-bg) 100%);
}

.hero-container {
    text-align: center;
    max-width: 800px;
    padding: 0 20px;
}

.hero-title {
    font-size: 4rem;
    font-weight: 700;
    margin-bottom: 1rem;
    background: linear-gradient(135deg, var(--text-primary), var(--accent-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: fadeInUp 1s ease;
}

.hero-subtitle {
    font-size: 2rem;
    font-weight: 500;
    color: var(--accent-color);
    margin-bottom: 2rem;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    animation: fadeInUp 1s ease 0.2s both;
}

.cursor {
    animation: blink 1s infinite;
}

.hero-description {
    font-size: 1.2rem;
    color: var(--text-secondary);
    margin-bottom: 3rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
    animation: fadeInUp 1s ease 0.4s both;
}

.hero-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
    animation: fadeInUp 1s ease 0.6s both;
}

.btn {
    padding: 12px 30px;
    border: none;
    border-radius: 50px;
    font-size: 1rem;
    font-weight: 600;
    text-decoration: none;
    cursor: pointer;
    transition: var(--transition);
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.btn-primary {
    background: var(--accent-color);
    color: white;
}

.btn-primary:hover {
    background: var(--accent-hover);
    transform: translateY(-2px);
    box-shadow: var(--shadow);
}

.btn-secondary {
    background: transparent;
    color: var(--text-primary);
    border: 2px solid var(--text-primary);
}

.btn-secondary:hover {
    background: var(--text-primary);
    color: var(--primary-bg);
    transform: translateY(-2px);
}

.scroll-indicator {
    position: absolute;
    bottom: 30px;
    left: 50%;
    transform: translateX(-50%);
    animation: bounce 2s infinite;
}

.scroll-arrow {
    width: 20px;
    height: 20px;
    border-right: 2px solid var(--accent-color);
    border-bottom: 2px solid var(--accent-color);
    transform: rotate(45deg);
}

/* Section Styles */
section {
    padding: 100px 0;
}

.section-title {
    font-size: 3rem;
    font-weight: 700;
    text-align: center;
    margin-bottom: 4rem;
    color: var(--text-primary);
    position: relative;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 4px;
    background: var(--accent-color);
    border-radius: 2px;
}

/* About Section */
.about {
    background: var(--secondary-bg);
}

.about-content {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 4rem;
    align-items: center;
}

.about-text p {
    font-size: 1.2rem;
    color: var(--text-secondary);
    line-height: 1.8;
}

.about-interests {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 2rem;
}

.interest-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
    padding: 2rem;
    background: var(--primary-bg);
    border-radius: 15px;
    transition: var(--transition);
}

.interest-item:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow);
}

.interest-item i {
    font-size: 2rem;
    color: var(--accent-color);
}

.interest-item span {
    font-weight: 500;
    color: var(--text-primary);
}

/* Skills Section */
.skills-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 2rem;
    max-width: 800px;
    margin: 0 auto;
}

.skill-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
    padding: 2rem;
    background: var(--secondary-bg);
    border-radius: 15px;
    transition: var(--transition);
    cursor: pointer;
}

.skill-item:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow);
    background: var(--accent-color);
}

.skill-item i {
    font-size: 3rem;
    color: var(--accent-color);
    transition: var(--transition);
}

.skill-item:hover i {
    color: white;
}

.skill-name {
    font-weight: 600;
    color: var(--text-primary);
    opacity: 0;
    transform: translateY(10px);
    transition: var(--transition);
}

.skill-item:hover .skill-name {
    opacity: 1;
    transform: translateY(0);
    color: white;
}

/* Projects Section */
.projects {
    background: var(--secondary-bg);
}

.projects-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 3rem;
}

.project-card {
    background: var(--primary-bg);
    border-radius: 20px;
    overflow: hidden;
    transition: var(--transition);
    border: 1px solid var(--border-color);
}

.project-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow);
}

.project-image {
    position: relative;
    height: 250px;
    overflow: hidden;
}

.project-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition);
}

.project-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 123, 255, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: var(--transition);
}

.project-card:hover .project-overlay {
    opacity: 1;
}

.project-links {
    display: flex;
    gap: 1rem;
}

.project-link {
    padding: 10px 20px;
    background: white;
    color: var(--accent-color);
    text-decoration: none;
    border-radius: 25px;
    font-weight: 600;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.project-link:hover {
    background: var(--accent-color);
    color: white;
}

.project-content {
    padding: 2rem;
}

.project-title {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: var(--text-primary);
}

.project-description {
    color: var(--text-secondary);
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

.project-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.tag {
    padding: 5px 12px;
    background: var(--accent-color);
    color: white;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 500;
}

/* Contact Section */
.contact-content {
    max-width: 600px;
    margin: 0 auto;
    text-align: center;
}

.contact-form {
    margin-bottom: 3rem;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 15px;
    background: var(--secondary-bg);
    border: 2px solid var(--border-color);
    border-radius: 10px;
    color: var(--text-primary);
    font-size: 1rem;
    transition: var(--transition);
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--accent-color);
}

.form-group input::placeholder,
.form-group textarea::placeholder {
    color: var(--text-secondary);
}

.social-links {
    display: flex;
    justify-content: center;
    gap: 2rem;
}

.social-link {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 50px;
    height: 50px;
    background: var(--secondary-bg);
    color: var(--text-primary);
    border-radius: 50%;
    text-decoration: none;
    font-size: 1.5rem;
    transition: var(--transition);
}

.social-link:hover {
    background: var(--accent-color);
    color: white;
    transform: translateY(-3px);
}

/* Footer */
.footer {
    background: var(--primary-bg);
    padding: 2rem 0;
    text-align: center;
    border-top: 1px solid var(--border-color);
}

.footer p {
    color: var(--text-secondary);
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes blink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0; }
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateX(-50%) translateY(0);
    }
    40% {
        transform: translateX(-50%) translateY(-10px);
    }
    60% {
        transform: translateX(-50%) translateY(-5px);
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-menu {
        position: fixed;
        left: -100%;
        top: 70px;
        flex-direction: column;
        background-color: var(--primary-bg);
        width: 100%;
        text-align: center;
        transition: 0.3s;
        box-shadow: var(--shadow);
        padding: 2rem 0;
    }

    .nav-menu.active {
        left: 0;
    }

    .nav-toggle {
        display: flex;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .hero-subtitle {
        font-size: 1.5rem;
    }

    .hero-description {
        font-size: 1rem;
    }

    .hero-buttons {
        flex-direction: column;
        align-items: center;
    }

    .section-title {
        font-size: 2rem;
    }

    .about-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .about-interests {
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
    }

    .skills-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
    }

    .projects-grid {
        grid-template-columns: 1fr;
    }

    .project-links {
        flex-direction: column;
    }
}
