<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CV - Le Anh Tu</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            line-height: 1.6;
            color: #333;
            background: #f5f5f5;
        }

        .cv-container {
            max-width: 210mm;
            margin: 20px auto;
            background: white;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
            min-height: 297mm;
        }

        .cv-header {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            padding: 40px;
            text-align: center;
        }

        .cv-name {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 10px;
        }

        .cv-title {
            font-size: 1.2rem;
            font-weight: 400;
            opacity: 0.9;
            margin-bottom: 20px;
        }

        .cv-contact {
            display: flex;
            justify-content: center;
            gap: 30px;
            flex-wrap: wrap;
            font-size: 0.9rem;
        }

        .cv-contact-item {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .cv-body {
            padding: 40px;
        }

        .cv-section {
            margin-bottom: 40px;
        }

        .cv-section-title {
            font-size: 1.4rem;
            font-weight: 700;
            color: #007bff;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #007bff;
        }

        .cv-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 40px;
        }

        .cv-item {
            margin-bottom: 25px;
        }

        .cv-item-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: #333;
            margin-bottom: 5px;
        }

        .cv-item-subtitle {
            font-size: 0.9rem;
            color: #666;
            margin-bottom: 10px;
        }

        .cv-item-description {
            font-size: 0.9rem;
            color: #555;
            line-height: 1.6;
        }

        .skills-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
        }

        .skill-category {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #007bff;
        }

        .skill-category h4 {
            color: #007bff;
            margin-bottom: 15px;
            font-size: 1rem;
        }

        .skill-list {
            list-style: none;
        }

        .skill-list li {
            padding: 5px 0;
            font-size: 0.9rem;
            color: #555;
        }

        .skill-list li::before {
            content: "▪";
            color: #007bff;
            margin-right: 10px;
        }

        .projects-grid {
            display: grid;
            gap: 25px;
        }

        .project-item {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 8px;
            border-left: 4px solid #007bff;
        }

        .project-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: #007bff;
            margin-bottom: 10px;
        }

        .project-tech {
            font-size: 0.8rem;
            color: #666;
            margin-bottom: 15px;
            font-style: italic;
        }

        .project-description {
            font-size: 0.9rem;
            color: #555;
            line-height: 1.6;
        }

        @media print {
            body {
                background: white;
            }
            .cv-container {
                box-shadow: none;
                margin: 0;
            }
        }

        @media (max-width: 768px) {
            .cv-grid {
                grid-template-columns: 1fr;
                gap: 30px;
            }
            
            .cv-contact {
                flex-direction: column;
                gap: 15px;
            }
            
            .skills-grid {
                grid-template-columns: 1fr;
            }
            
            .cv-container {
                margin: 10px;
            }
            
            .cv-header, .cv-body {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="cv-container">
        <!-- Header -->
        <div class="cv-header">
            <h1 class="cv-name">LE ANH TU</h1>
            <p class="cv-title">Front-end Developer</p>
            <div class="cv-contact">
                <div class="cv-contact-item">
                    <span>📧</span>
                    <span><EMAIL></span>
                </div>
                <div class="cv-contact-item">
                    <span>💻</span>
                    <span>github.com/leanhtu05</span>
                </div>
                <div class="cv-contact-item">
                    <span>🌐</span>
                    <span>leanhtu05.github.io/portfolio</span>
                </div>
                <div class="cv-contact-item">
                    <span>📱</span>
                    <span>+84 123 456 789</span>
                </div>
            </div>
        </div>

        <!-- Body -->
        <div class="cv-body">
            <!-- Objective -->
            <div class="cv-section">
                <h2 class="cv-section-title">MỤC TIÊU NGHỀ NGHIỆP</h2>
                <p class="cv-item-description">
                    Trở thành Front-end Developer chuyên nghiệp với khả năng xây dựng các ứng dụng web hiện đại, 
                    tối ưu trải nghiệm người dùng và áp dụng các công nghệ mới nhất. Mong muốn đóng góp vào 
                    các dự án có tác động tích cực và phát triển kỹ năng trong môi trường làm việc chuyên nghiệp.
                </p>
            </div>

            <!-- Skills -->
            <div class="cv-section">
                <h2 class="cv-section-title">KỸ NĂNG CHUYÊN MÔN</h2>
                <div class="skills-grid">
                    <div class="skill-category">
                        <h4>Frontend Development</h4>
                        <ul class="skill-list">
                            <li>HTML5 & CSS3</li>
                            <li>JavaScript ES6+</li>
                            <li>Responsive Design</li>
                            <li>CSS Grid & Flexbox</li>
                            <li>SASS/SCSS</li>
                        </ul>
                    </div>
                    <div class="skill-category">
                        <h4>Frameworks & Libraries</h4>
                        <ul class="skill-list">
                            <li>Flutter</li>
                            <li>Chart.js</li>
                            <li>Bootstrap</li>
                            <li>jQuery</li>
                        </ul>
                    </div>
                    <div class="skill-category">
                        <h4>Tools & Technologies</h4>
                        <ul class="skill-list">
                            <li>Git & GitHub</li>
                            <li>Firebase</li>
                            <li>VS Code</li>
                            <li>Figma</li>
                            <li>Chrome DevTools</li>
                        </ul>
                    </div>
                    <div class="skill-category">
                        <h4>Soft Skills</h4>
                        <ul class="skill-list">
                            <li>Problem Solving</li>
                            <li>Team Collaboration</li>
                            <li>Time Management</li>
                            <li>Continuous Learning</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- Projects -->
            <div class="cv-section">
                <h2 class="cv-section-title">DỰ ÁN NỔI BẬT</h2>
                <div class="projects-grid">
                    <div class="project-item">
                        <h3 class="project-title">Portfolio Website</h3>
                        <p class="project-tech">HTML5, CSS3, JavaScript, Responsive Design</p>
                        <p class="project-description">
                            Xây dựng website portfolio cá nhân với 4+ demo projects tương tác, bao gồm admin dashboard, 
                            e-commerce site và SaaS landing pages. Thiết kế responsive, tối ưu SEO và hiệu suất.
                            <br><strong>Link:</strong> leanhtu05.github.io/portfolio
                        </p>
                    </div>
                    
                    <div class="project-item">
                        <h3 class="project-title">Health AI - Admin Dashboard</h3>
                        <p class="project-tech">Flutter Web, Firebase, Chart.js</p>
                        <p class="project-description">
                            Phát triển trang quản trị cho ứng dụng AI chẩn đoán sức khỏe với data visualization, 
                            user management và real-time analytics. Sử dụng Flutter Web để tạo UI responsive 
                            và tích hợp Firebase cho backend.
                        </p>
                    </div>
                    
                    <div class="project-item">
                        <h3 class="project-title">E-commerce Landing Pages</h3>
                        <p class="project-tech">HTML5, CSS3, JavaScript, Responsive Design</p>
                        <p class="project-description">
                            Thiết kế và phát triển các landing page cho thương mại điện tử với shopping cart, 
                            product showcase, countdown timer và payment integration. Tối ưu conversion rate 
                            và mobile experience.
                        </p>
                    </div>
                </div>
            </div>

            <!-- Education & Experience -->
            <div class="cv-grid">
                <div class="cv-section">
                    <h2 class="cv-section-title">HỌC VẤN</h2>
                    <div class="cv-item">
                        <h3 class="cv-item-title">Đại học Công nghệ Thông tin</h3>
                        <p class="cv-item-subtitle">Cử nhân Công nghệ Thông tin • 2021-2025</p>
                        <p class="cv-item-description">
                            Chuyên ngành: Công nghệ Phần mềm<br>
                            GPA: 3.5/4.0<br>
                            Đồ án tốt nghiệp: Ứng dụng AI chẩn đoán sức khỏe
                        </p>
                    </div>
                </div>

                <div class="cv-section">
                    <h2 class="cv-section-title">CHỨNG CHỈ</h2>
                    <div class="cv-item">
                        <h3 class="cv-item-title">Responsive Web Design</h3>
                        <p class="cv-item-subtitle">FreeCodeCamp • 2024</p>
                    </div>
                    <div class="cv-item">
                        <h3 class="cv-item-title">JavaScript Algorithms</h3>
                        <p class="cv-item-subtitle">FreeCodeCamp • 2024</p>
                    </div>
                    <div class="cv-item">
                        <h3 class="cv-item-title">Git & GitHub</h3>
                        <p class="cv-item-subtitle">Coursera • 2023</p>
                    </div>
                </div>
            </div>

            <!-- Languages & Interests -->
            <div class="cv-grid">
                <div class="cv-section">
                    <h2 class="cv-section-title">NGÔN NGỮ</h2>
                    <div class="cv-item">
                        <h3 class="cv-item-title">Tiếng Việt</h3>
                        <p class="cv-item-subtitle">Bản ngữ</p>
                    </div>
                    <div class="cv-item">
                        <h3 class="cv-item-title">Tiếng Anh</h3>
                        <p class="cv-item-subtitle">Trung cấp (B2) - Đọc hiểu tài liệu kỹ thuật tốt</p>
                    </div>
                </div>

                <div class="cv-section">
                    <h2 class="cv-section-title">SỞ THÍCH</h2>
                    <ul class="skill-list">
                        <li>Lập trình & học công nghệ mới</li>
                        <li>Thiết kế UI/UX</li>
                        <li>Đọc sách công nghệ</li>
                        <li>Chơi bóng đá</li>
                        <li>Nghe nhạc</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Print functionality
        function printCV() {
            window.print();
        }

        // Add print button if needed
        document.addEventListener('keydown', function(e) {
            if (e.ctrlKey && e.key === 'p') {
                e.preventDefault();
                printCV();
            }
        });
    </script>
</body>
</html>
