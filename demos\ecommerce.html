<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>StyleHub - Premium Fashion Store</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;500;600;700&family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            --primary-color: #d4af37;
            --primary-dark: #b8941f;
            --secondary-color: #2c2c2c;
            --text-primary: #1a1a1a;
            --text-secondary: #666;
            --text-light: #999;
            --bg-light: #f8f8f8;
            --white: #ffffff;
            --shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            --shadow-hover: 0 20px 40px rgba(0, 0, 0, 0.15);
            --transition: all 0.3s ease;
        }

        body {
            font-family: 'Inter', sans-serif;
            line-height: 1.6;
            color: var(--text-primary);
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        /* Navigation */
        .navbar {
            position: fixed;
            top: 0;
            width: 100%;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            z-index: 1000;
            padding: 1rem 0;
            border-bottom: 1px solid #eee;
            transition: var(--transition);
        }

        .nav-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            font-family: 'Playfair Display', serif;
            font-size: 2rem;
            font-weight: 700;
            color: var(--primary-color);
        }

        .nav-menu {
            display: flex;
            gap: 2rem;
            list-style: none;
        }

        .nav-link {
            color: var(--text-primary);
            text-decoration: none;
            font-weight: 500;
            transition: var(--transition);
            position: relative;
        }

        .nav-link:hover {
            color: var(--primary-color);
        }

        .nav-actions {
            display: flex;
            gap: 1rem;
            align-items: center;
        }

        .nav-icon {
            position: relative;
            color: var(--text-primary);
            font-size: 1.2rem;
            cursor: pointer;
            transition: var(--transition);
        }

        .nav-icon:hover {
            color: var(--primary-color);
        }

        .cart-count {
            position: absolute;
            top: -8px;
            right: -8px;
            background: var(--primary-color);
            color: white;
            border-radius: 50%;
            width: 18px;
            height: 18px;
            font-size: 0.7rem;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        /* Hero Section */
        .hero {
            height: 100vh;
            background: linear-gradient(rgba(0, 0, 0, 0.4), rgba(0, 0, 0, 0.4)), 
                        url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1200 800"><rect fill="%23f8f8f8" width="1200" height="800"/><rect fill="%23d4af37" x="0" y="0" width="600" height="400" opacity="0.1"/><rect fill="%232c2c2c" x="600" y="400" width="600" height="400" opacity="0.05"/></svg>');
            background-size: cover;
            background-position: center;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            color: white;
            position: relative;
        }

        .hero-content {
            max-width: 800px;
            padding: 0 20px;
        }

        .hero-badge {
            display: inline-block;
            background: rgba(212, 175, 55, 0.2);
            color: var(--primary-color);
            padding: 0.5rem 1.5rem;
            border-radius: 50px;
            font-size: 0.9rem;
            font-weight: 600;
            margin-bottom: 2rem;
            border: 1px solid var(--primary-color);
            animation: fadeInUp 0.8s ease;
        }

        .hero-title {
            font-family: 'Playfair Display', serif;
            font-size: 4.5rem;
            font-weight: 700;
            line-height: 1.1;
            margin-bottom: 1.5rem;
            animation: fadeInUp 0.8s ease 0.2s both;
        }

        .hero-subtitle {
            font-size: 1.3rem;
            margin-bottom: 3rem;
            opacity: 0.9;
            animation: fadeInUp 0.8s ease 0.4s both;
        }

        .hero-buttons {
            display: flex;
            gap: 1rem;
            justify-content: center;
            flex-wrap: wrap;
            animation: fadeInUp 0.8s ease 0.6s both;
        }

        .btn {
            padding: 1rem 2.5rem;
            border: none;
            border-radius: 50px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition);
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .btn-primary {
            background: var(--primary-color);
            color: white;
        }

        .btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-3px);
            box-shadow: var(--shadow-hover);
        }

        .btn-secondary {
            background: transparent;
            color: white;
            border: 2px solid white;
        }

        .btn-secondary:hover {
            background: white;
            color: var(--text-primary);
        }

        /* Featured Products */
        .featured {
            padding: 100px 0;
            background: var(--white);
        }

        .section-header {
            text-align: center;
            margin-bottom: 4rem;
        }

        .section-badge {
            display: inline-block;
            background: var(--bg-light);
            color: var(--primary-color);
            padding: 0.5rem 1rem;
            border-radius: 50px;
            font-size: 0.9rem;
            font-weight: 600;
            margin-bottom: 1rem;
        }

        .section-title {
            font-family: 'Playfair Display', serif;
            font-size: 3rem;
            font-weight: 700;
            margin-bottom: 1rem;
            color: var(--text-primary);
        }

        .section-subtitle {
            font-size: 1.2rem;
            color: var(--text-secondary);
            max-width: 600px;
            margin: 0 auto;
        }

        .products-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 3rem;
        }

        .product-card {
            background: white;
            border-radius: 20px;
            overflow: hidden;
            box-shadow: var(--shadow);
            transition: var(--transition);
            position: relative;
        }

        .product-card:hover {
            transform: translateY(-10px);
            box-shadow: var(--shadow-hover);
        }

        .product-image {
            height: 300px;
            background: var(--bg-light);
            position: relative;
            overflow: hidden;
        }

        .product-image::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 80px;
            height: 80px;
            background: var(--primary-color);
            border-radius: 50%;
            opacity: 0.1;
        }

        .product-badge {
            position: absolute;
            top: 1rem;
            left: 1rem;
            background: var(--primary-color);
            color: white;
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
        }

        .product-actions {
            position: absolute;
            top: 1rem;
            right: 1rem;
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
            opacity: 0;
            transition: var(--transition);
        }

        .product-card:hover .product-actions {
            opacity: 1;
        }

        .action-btn {
            width: 40px;
            height: 40px;
            background: white;
            border: none;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .action-btn:hover {
            background: var(--primary-color);
            color: white;
        }

        .product-info {
            padding: 2rem;
        }

        .product-category {
            color: var(--text-light);
            font-size: 0.9rem;
            margin-bottom: 0.5rem;
        }

        .product-title {
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 1rem;
            color: var(--text-primary);
        }

        .product-price {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 1.5rem;
        }

        .current-price {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--primary-color);
        }

        .old-price {
            font-size: 1.1rem;
            color: var(--text-light);
            text-decoration: line-through;
        }

        .discount {
            background: #ff4757;
            color: white;
            padding: 0.25rem 0.5rem;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 600;
        }

        .product-rating {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-bottom: 1.5rem;
        }

        .stars {
            color: #ffd700;
        }

        .rating-text {
            color: var(--text-light);
            font-size: 0.9rem;
        }

        .add-to-cart {
            width: 100%;
            background: var(--text-primary);
            color: white;
            border: none;
            padding: 1rem;
            border-radius: 50px;
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition);
        }

        .add-to-cart:hover {
            background: var(--primary-color);
        }

        /* Special Offer */
        .special-offer {
            padding: 100px 0;
            background: var(--primary-color);
            color: white;
            text-align: center;
        }

        .offer-content {
            max-width: 800px;
            margin: 0 auto;
        }

        .offer-title {
            font-family: 'Playfair Display', serif;
            font-size: 3.5rem;
            font-weight: 700;
            margin-bottom: 1rem;
        }

        .offer-subtitle {
            font-size: 1.3rem;
            margin-bottom: 2rem;
            opacity: 0.9;
        }

        .offer-timer {
            display: flex;
            justify-content: center;
            gap: 2rem;
            margin-bottom: 3rem;
        }

        .timer-item {
            text-align: center;
        }

        .timer-number {
            font-size: 3rem;
            font-weight: 700;
            display: block;
        }

        .timer-label {
            font-size: 0.9rem;
            opacity: 0.8;
        }

        .back-to-portfolio {
            position: fixed;
            top: 20px;
            right: 20px;
            background: var(--primary-color);
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            text-decoration: none;
            font-weight: 500;
            transition: var(--transition);
            z-index: 1001;
        }

        .back-to-portfolio:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }

        /* Footer */
        .footer {
            background: var(--secondary-color);
            color: white;
            padding: 3rem 0;
            text-align: center;
        }

        /* Animations */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .animate-on-scroll {
            opacity: 0;
            transform: translateY(30px);
            transition: all 0.8s ease;
        }

        .animate-on-scroll.animated {
            opacity: 1;
            transform: translateY(0);
        }

        /* Mobile Responsive */
        @media (max-width: 768px) {
            .nav-menu {
                display: none;
            }

            .hero-title {
                font-size: 2.5rem;
            }

            .section-title {
                font-size: 2rem;
            }

            .offer-title {
                font-size: 2.5rem;
            }

            .products-grid {
                grid-template-columns: 1fr;
            }

            .offer-timer {
                flex-wrap: wrap;
                gap: 1rem;
            }

            .hero-buttons {
                flex-direction: column;
                align-items: center;
            }
        }
    </style>
</head>
<body>
    <a href="../index.html" class="back-to-portfolio">
        <i class="fas fa-arrow-left"></i> Về Portfolio
    </a>

    <!-- Navigation -->
    <nav class="navbar">
        <div class="container">
            <div class="nav-container">
                <div class="logo">StyleHub</div>
                <ul class="nav-menu">
                    <li><a href="#home" class="nav-link">Trang chủ</a></li>
                    <li><a href="#products" class="nav-link">Sản phẩm</a></li>
                    <li><a href="#about" class="nav-link">Giới thiệu</a></li>
                    <li><a href="#contact" class="nav-link">Liên hệ</a></li>
                </ul>
                <div class="nav-actions">
                    <i class="fas fa-search nav-icon"></i>
                    <i class="fas fa-heart nav-icon"></i>
                    <div class="nav-icon">
                        <i class="fas fa-shopping-cart"></i>
                        <span class="cart-count">3</span>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section id="home" class="hero">
        <div class="hero-content">
            <div class="hero-badge">Bộ sưu tập mới 2025</div>
            <h1 class="hero-title">
                Thời trang cao cấp<br>
                cho phong cách riêng
            </h1>
            <p class="hero-subtitle">
                Khám phá bộ sưu tập thời trang độc đáo với chất lượng premium, 
                thiết kế tinh tế và phong cách hiện đại.
            </p>
            <div class="hero-buttons">
                <a href="#products" class="btn btn-primary">
                    <i class="fas fa-shopping-bag"></i>
                    Mua sắm ngay
                </a>
                <a href="#about" class="btn btn-secondary">
                    <i class="fas fa-play"></i>
                    Xem bộ sưu tập
                </a>
            </div>
        </div>
    </section>

    <!-- Featured Products -->
    <section id="products" class="featured">
        <div class="container">
            <div class="section-header animate-on-scroll">
                <div class="section-badge">Sản phẩm nổi bật</div>
                <h2 class="section-title">Bộ sưu tập đặc biệt</h2>
                <p class="section-subtitle">
                    Những sản phẩm được lựa chọn kỹ lưỡng với chất lượng cao nhất
                    và thiết kế độc đáo, phù hợp với mọi phong cách.
                </p>
            </div>
            <div class="products-grid">
                <div class="product-card animate-on-scroll">
                    <div class="product-image">
                        <div class="product-badge">Mới</div>
                        <div class="product-actions">
                            <button class="action-btn" title="Yêu thích">
                                <i class="fas fa-heart"></i>
                            </button>
                            <button class="action-btn" title="Xem nhanh">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="action-btn" title="So sánh">
                                <i class="fas fa-exchange-alt"></i>
                            </button>
                        </div>
                    </div>
                    <div class="product-info">
                        <div class="product-category">Áo sơ mi</div>
                        <h3 class="product-title">Áo sơ mi Premium Cotton</h3>
                        <div class="product-price">
                            <span class="current-price">1.299.000₫</span>
                            <span class="old-price">1.599.000₫</span>
                            <span class="discount">-19%</span>
                        </div>
                        <div class="product-rating">
                            <div class="stars">
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                            </div>
                            <span class="rating-text">(128 đánh giá)</span>
                        </div>
                        <button class="add-to-cart">
                            <i class="fas fa-shopping-cart"></i>
                            Thêm vào giỏ hàng
                        </button>
                    </div>
                </div>

                <div class="product-card animate-on-scroll">
                    <div class="product-image">
                        <div class="product-badge">Bán chạy</div>
                        <div class="product-actions">
                            <button class="action-btn" title="Yêu thích">
                                <i class="fas fa-heart"></i>
                            </button>
                            <button class="action-btn" title="Xem nhanh">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="action-btn" title="So sánh">
                                <i class="fas fa-exchange-alt"></i>
                            </button>
                        </div>
                    </div>
                    <div class="product-info">
                        <div class="product-category">Quần jeans</div>
                        <h3 class="product-title">Quần Jeans Slim Fit</h3>
                        <div class="product-price">
                            <span class="current-price">899.000₫</span>
                        </div>
                        <div class="product-rating">
                            <div class="stars">
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="far fa-star"></i>
                            </div>
                            <span class="rating-text">(89 đánh giá)</span>
                        </div>
                        <button class="add-to-cart">
                            <i class="fas fa-shopping-cart"></i>
                            Thêm vào giỏ hàng
                        </button>
                    </div>
                </div>

                <div class="product-card animate-on-scroll">
                    <div class="product-image">
                        <div class="product-badge">Giảm giá</div>
                        <div class="product-actions">
                            <button class="action-btn" title="Yêu thích">
                                <i class="fas fa-heart"></i>
                            </button>
                            <button class="action-btn" title="Xem nhanh">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="action-btn" title="So sánh">
                                <i class="fas fa-exchange-alt"></i>
                            </button>
                        </div>
                    </div>
                    <div class="product-info">
                        <div class="product-category">Áo khoác</div>
                        <h3 class="product-title">Áo khoác Blazer Luxury</h3>
                        <div class="product-price">
                            <span class="current-price">2.199.000₫</span>
                            <span class="old-price">2.799.000₫</span>
                            <span class="discount">-21%</span>
                        </div>
                        <div class="product-rating">
                            <div class="stars">
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                            </div>
                            <span class="rating-text">(156 đánh giá)</span>
                        </div>
                        <button class="add-to-cart">
                            <i class="fas fa-shopping-cart"></i>
                            Thêm vào giỏ hàng
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Special Offer -->
    <section class="special-offer">
        <div class="container">
            <div class="offer-content animate-on-scroll">
                <h2 class="offer-title">Flash Sale 50%</h2>
                <p class="offer-subtitle">
                    Ưu đãi đặc biệt chỉ trong thời gian có hạn.
                    Đừng bỏ lỡ cơ hội sở hữu những sản phẩm yêu thích với giá tốt nhất!
                </p>
                <div class="offer-timer">
                    <div class="timer-item">
                        <span class="timer-number" id="hours">12</span>
                        <span class="timer-label">Giờ</span>
                    </div>
                    <div class="timer-item">
                        <span class="timer-number" id="minutes">34</span>
                        <span class="timer-label">Phút</span>
                    </div>
                    <div class="timer-item">
                        <span class="timer-number" id="seconds">56</span>
                        <span class="timer-label">Giây</span>
                    </div>
                </div>
                <a href="#products" class="btn btn-secondary" style="background: white; color: var(--primary-color);">
                    <i class="fas fa-bolt"></i>
                    Mua ngay
                </a>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <p>&copy; 2025 StyleHub. Thiết kế bởi Le Anh Tu - Front-end Developer</p>
        </div>
    </footer>

    <script>
        // Smooth scrolling
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Scroll animations
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animated');
                }
            });
        }, observerOptions);

        document.querySelectorAll('.animate-on-scroll').forEach(el => {
            observer.observe(el);
        });

        // Countdown timer
        function updateTimer() {
            const now = new Date().getTime();
            const endTime = now + (12 * 60 * 60 * 1000) + (34 * 60 * 1000) + (56 * 1000); // 12:34:56 from now

            setInterval(() => {
                const currentTime = new Date().getTime();
                const timeLeft = endTime - currentTime;

                const hours = Math.floor((timeLeft % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
                const minutes = Math.floor((timeLeft % (1000 * 60 * 60)) / (1000 * 60));
                const seconds = Math.floor((timeLeft % (1000 * 60)) / 1000);

                document.getElementById('hours').textContent = hours.toString().padStart(2, '0');
                document.getElementById('minutes').textContent = minutes.toString().padStart(2, '0');
                document.getElementById('seconds').textContent = seconds.toString().padStart(2, '0');

                if (timeLeft < 0) {
                    document.getElementById('hours').textContent = '00';
                    document.getElementById('minutes').textContent = '00';
                    document.getElementById('seconds').textContent = '00';
                }
            }, 1000);
        }

        updateTimer();

        // Add to cart functionality
        document.querySelectorAll('.add-to-cart').forEach(button => {
            button.addEventListener('click', function() {
                const cartCount = document.querySelector('.cart-count');
                let currentCount = parseInt(cartCount.textContent);
                cartCount.textContent = currentCount + 1;

                // Visual feedback
                this.innerHTML = '<i class="fas fa-check"></i> Đã thêm';
                this.style.background = 'var(--primary-color)';

                setTimeout(() => {
                    this.innerHTML = '<i class="fas fa-shopping-cart"></i> Thêm vào giỏ hàng';
                    this.style.background = 'var(--text-primary)';
                }, 2000);
            });
        });

        // Wishlist functionality
        document.querySelectorAll('.action-btn').forEach(button => {
            button.addEventListener('click', function() {
                if (this.querySelector('.fa-heart')) {
                    this.style.background = 'var(--primary-color)';
                    this.style.color = 'white';
                    setTimeout(() => {
                        this.style.background = 'white';
                        this.style.color = 'var(--text-primary)';
                    }, 1000);
                }
            });
        });

        // Navbar background on scroll
        window.addEventListener('scroll', () => {
            const navbar = document.querySelector('.navbar');
            if (window.scrollY > 100) {
                navbar.style.background = 'rgba(255, 255, 255, 0.98)';
                navbar.style.boxShadow = '0 2px 20px rgba(0, 0, 0, 0.1)';
            } else {
                navbar.style.background = 'rgba(255, 255, 255, 0.95)';
                navbar.style.boxShadow = 'none';
            }
        });
    </script>
</body>
</html>
