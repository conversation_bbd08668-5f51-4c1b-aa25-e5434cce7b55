# Le Anh Tu - Portfolio Website

A modern, minimalist portfolio website for a front-end developer, built with HTML, CSS, and JavaScript.

## 🌟 Features

- **Modern Design**: Clean, minimalist dark theme with professional aesthetics
- **Responsive Layout**: Fully responsive design that works on desktop, tablet, and mobile
- **Interactive Elements**: 
  - Typing effect animation for role titles
  - Smooth scroll animations
  - Hover effects on cards and buttons
  - 3D tilt effect on project cards
- **Single Page Application**: Smooth navigation between sections
- **Contact Form**: Functional contact form with validation
- **Mobile Navigation**: Hamburger menu for mobile devices

## 🎨 Design Specifications

- **Color Scheme**: 
  - Primary Background: `#1a1a1a`
  - Secondary Background: `#2a2a2a`
  - Text Primary: `#f0f0f0`
  - Text Secondary: `#b0b0b0`
  - Accent Color: `#007bff`
- **Typography**: Inter font family from Google Fonts
- **Layout**: CSS Grid and Flexbox for responsive design

## 📱 Sections

1. **Hero Section**: Introduction with typing effect and call-to-action buttons
2. **About Me**: Personal introduction and interests
3. **Skills**: Technology stack with interactive icons
4. **Projects**: Portfolio projects with hover effects and links
5. **Contact**: Contact form and social media links
6. **Footer**: Simple footer with copyright

## 🚀 Technologies Used

- HTML5
- CSS3 (Grid, Flexbox, Animations)
- Vanilla JavaScript
- Font Awesome Icons
- Google Fonts (Inter)

## 📂 Project Structure

```
portfolio/
├── index.html          # Main HTML file
├── styles.css          # CSS styles
├── script.js           # JavaScript functionality
├── assets/             # Assets folder
│   ├── cv.pdf         # CV file
│   ├── health-ai-admin.jpg  # Project image 1
│   └── landing-pages.jpg    # Project image 2
└── README.md          # Project documentation
```

## 🛠️ Setup and Usage

1. Clone or download the project files
2. Open `index.html` in a web browser
3. The website is ready to use!

## 📋 Customization

To customize the portfolio for your own use:

1. **Personal Information**: Update name, description, and contact details in `index.html`
2. **Projects**: Replace project information and images in the projects section
3. **Skills**: Modify the skills grid to reflect your technology stack
4. **Colors**: Adjust CSS custom properties in `:root` selector in `styles.css`
5. **Images**: Replace placeholder images in the `assets/` folder
6. **CV**: Replace `assets/cv.pdf` with your actual CV

## ✨ Special Features

- **Typing Effect**: Animated typing effect for role titles
- **Scroll Animations**: Elements animate into view as you scroll
- **3D Card Effects**: Project cards have subtle 3D tilt effects on hover
- **Mobile Responsive**: Hamburger menu and responsive grid layouts
- **Form Validation**: Contact form includes client-side validation
- **Easter Egg**: Hidden Konami code easter egg (↑↑↓↓←→←→BA)

## 🌐 Browser Support

- Chrome (recommended)
- Firefox
- Safari
- Edge

## 📄 License

This project is open source and available under the [MIT License](LICENSE).

## 👤 Author

**Le Anh Tu**
- Email: <EMAIL>
- LinkedIn: [linkedin.com/in/leanhttu](https://linkedin.com/in/leanhttu)
- GitHub: [github.com/leanhttu](https://github.com/leanhttu)

---

*Designed and coded with ❤️ by Le Anh Tu*
