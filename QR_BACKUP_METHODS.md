# 📱 Các cách tạo QR Code cho Portfolio

## 🎯 **URL Portfolio của bạn:**
```
https://leanhtu05.github.io/portfolio/
```

---

## 🚀 **Phương pháp 1: Sử dụng file simple-qr.html (Khuyến nghị)**

1. Mở file `simple-qr.html` trong trình duyệt
2. QR code sẽ tự động hiển thị
3. Click "Tải QR Code" để download
4. Chọn kích thước phù hợp:
   - **500x500**: Cho CV thông thường
   - **1000x1000**: Cho CV chất lượng cao

---

## 🌐 **Phương pháp 2: Sử dụng website online**

### **QR Code Generator (qr-code-generator.com):**
1. Truy cập: https://www.qr-code-generator.com
2. Chọn "URL"
3. Nhập: `https://leanhtu05.github.io/portfolio/`
4. Tùy chỉnh màu sắc (khuyến nghị: #007bff)
5. Tải xuống PNG

### **QR Server API (Trực tiếp):**
1. Truy cập link sau trong trình duyệt:
```
https://api.qrserver.com/v1/create-qr-code/?size=500x500&data=https://leanhtu05.github.io/portfolio/
```
2. Click chuột phải → "Save image as..."
3. Lưu với tên: `portfolio-qr-code.png`

### **Google Charts API (Backup):**
```
https://chart.googleapis.com/chart?chs=500x500&cht=qr&chl=https://leanhtu05.github.io/portfolio/
```

---

## 📱 **Phương pháp 3: Sử dụng điện thoại**

### **iPhone:**
1. Mở Camera app
2. Quét QR code từ màn hình máy tính
3. Screenshot QR code hiển thị
4. Crop và sử dụng

### **Android:**
1. Mở Google Lens hoặc Camera
2. Quét QR code từ màn hình
3. Screenshot và crop

---

## 🖼️ **Phương pháp 4: Tạo QR Code bằng ứng dụng**

### **Trên máy tính:**
- **QR Code Generator** (Microsoft Store)
- **QR Code Desktop Reader & Generator**

### **Trên điện thoại:**
- **QR Code Generator** (iOS/Android)
- **QR & Barcode Scanner**

---

## ✅ **Kiểm tra QR Code:**

Sau khi tạo QR code, hãy test:
1. Sử dụng điện thoại quét QR code
2. Đảm bảo nó dẫn đến: `https://leanhtu05.github.io/portfolio/`
3. Kiểm tra website load đúng

---

## 📄 **Cách sử dụng QR Code trong CV:**

### **Vị trí đặt QR Code:**
- ✅ **Góc trên phải** của CV (khuyến nghị)
- ✅ **Cuối CV** trong phần liên hệ
- ✅ **Bên cạnh thông tin Portfolio**

### **Kích thước:**
- **Tối thiểu**: 2cm x 2cm khi in
- **Khuyến nghị**: 2.5cm x 2.5cm
- **Tối đa**: 3cm x 3cm

### **Text đi kèm:**
```
📱 Scan để xem Portfolio
🌐 Portfolio Website
💻 Interactive Demos
```

### **Template CV:**
```
LE ANH TU
Front-end Developer

📧 <EMAIL>          [QR CODE]
💻 github.com/leanhtu05               Scan for
🌐 leanhtu05.github.io/portfolio/     Portfolio
```

---

## 🎨 **Tùy chỉnh QR Code (Nâng cao):**

### **Màu sắc:**
- **Foreground**: #1a1a1a (đen)
- **Background**: #ffffff (trắng)
- **Accent**: #007bff (xanh portfolio)

### **Logo trong QR Code:**
- Có thể thêm logo nhỏ ở giữa QR code
- Đảm bảo QR code vẫn scan được

---

## 🔧 **Troubleshooting:**

### **QR Code không tạo được:**
1. Kiểm tra kết nối internet
2. Thử API khác:
   - https://api.qrserver.com
   - https://chart.googleapis.com
   - https://quickchart.io

### **QR Code không scan được:**
1. Đảm bảo URL chính xác
2. Kiểm tra kích thước đủ lớn
3. Đảm bảo contrast tốt (đen trên trắng)

### **Website không load:**
1. Kiểm tra GitHub Pages đã active
2. Đợi 5-10 phút sau khi deploy
3. Test URL trực tiếp trong trình duyệt

---

## 📊 **Thống kê QR Code:**

Để theo dõi ai đã scan QR code, có thể sử dụng:
- **Google Analytics** (đã setup trong portfolio)
- **Bitly** (rút gọn URL và theo dõi clicks)
- **QR Code tracking services**

---

## 🎯 **Kết quả mong đợi:**

Sau khi hoàn thành, bạn sẽ có:
- ✅ QR Code chất lượng cao (PNG)
- ✅ Dẫn đến portfolio website
- ✅ Tăng tính chuyên nghiệp cho CV
- ✅ Dễ dàng chia sẻ với nhà tuyển dụng

---

## 🚀 **Tips sử dụng hiệu quả:**

1. **In thử QR code** trước khi nộp CV chính thức
2. **Test trên nhiều điện thoại** khác nhau
3. **Đặt QR code ở vị trí dễ thấy** nhưng không che thông tin quan trọng
4. **Thêm call-to-action** như "Scan để xem Portfolio"
5. **Đảm bảo website luôn hoạt động** trước khi nộp CV

**🎉 Chúc bạn thành công với QR Code Portfolio!**
