<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Health AI - Admin Dashboard</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            --primary-bg: #1a1a1a;
            --secondary-bg: #2a2a2a;
            --sidebar-bg: #1e1e1e;
            --text-primary: #f0f0f0;
            --text-secondary: #b0b0b0;
            --accent-color: #007bff;
            --accent-hover: #0056b3;
            --success-color: #28a745;
            --warning-color: #ffc107;
            --danger-color: #dc3545;
            --border-color: #333;
            --shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
            --transition: all 0.3s ease;
        }

        body {
            font-family: 'Inter', sans-serif;
            background-color: var(--primary-bg);
            color: var(--text-primary);
            line-height: 1.6;
        }

        .dashboard {
            display: flex;
            min-height: 100vh;
        }

        /* Sidebar */
        .sidebar {
            width: 250px;
            background: var(--sidebar-bg);
            padding: 2rem 0;
            position: fixed;
            height: 100vh;
            overflow-y: auto;
            transition: var(--transition);
            z-index: 1000;
        }

        .sidebar-header {
            padding: 0 2rem 2rem;
            border-bottom: 1px solid var(--border-color);
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 1rem;
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--accent-color);
        }

        .logo i {
            font-size: 2rem;
        }

        .sidebar-nav {
            padding: 2rem 0;
        }

        .nav-item {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 1rem 2rem;
            color: var(--text-secondary);
            text-decoration: none;
            transition: var(--transition);
            border-left: 3px solid transparent;
        }

        .nav-item:hover,
        .nav-item.active {
            background: rgba(0, 123, 255, 0.1);
            color: var(--accent-color);
            border-left-color: var(--accent-color);
        }

        .nav-item i {
            width: 20px;
            text-align: center;
        }

        /* Main Content */
        .main-content {
            flex: 1;
            margin-left: 250px;
            padding: 2rem;
            transition: var(--transition);
        }

        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid var(--border-color);
        }

        .header h1 {
            font-size: 2rem;
            font-weight: 700;
        }

        .header-actions {
            display: flex;
            gap: 1rem;
            align-items: center;
        }

        .btn {
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 8px;
            font-weight: 500;
            cursor: pointer;
            transition: var(--transition);
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .btn-primary {
            background: var(--accent-color);
            color: white;
        }

        .btn-primary:hover {
            background: var(--accent-hover);
        }

        .user-profile {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: var(--accent-color);
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 700;
        }

        /* Stats Cards */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
            margin-bottom: 3rem;
        }

        .stat-card {
            background: var(--secondary-bg);
            padding: 2rem;
            border-radius: 15px;
            border: 1px solid var(--border-color);
            transition: var(--transition);
        }

        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow);
        }

        .stat-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }

        .stat-title {
            color: var(--text-secondary);
            font-size: 0.9rem;
            font-weight: 500;
        }

        .stat-icon {
            width: 50px;
            height: 50px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
        }

        .stat-icon.users { background: rgba(40, 167, 69, 0.2); color: var(--success-color); }
        .stat-icon.sessions { background: rgba(0, 123, 255, 0.2); color: var(--accent-color); }
        .stat-icon.revenue { background: rgba(255, 193, 7, 0.2); color: var(--warning-color); }
        .stat-icon.growth { background: rgba(220, 53, 69, 0.2); color: var(--danger-color); }

        .stat-value {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }

        .stat-change {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.9rem;
        }

        .stat-change.positive { color: var(--success-color); }
        .stat-change.negative { color: var(--danger-color); }

        /* Charts Section */
        .charts-section {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 2rem;
            margin-bottom: 3rem;
        }

        .chart-card {
            background: var(--secondary-bg);
            padding: 2rem;
            border-radius: 15px;
            border: 1px solid var(--border-color);
        }

        .chart-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
        }

        .chart-title {
            font-size: 1.2rem;
            font-weight: 600;
        }

        .chart-container {
            position: relative;
            height: 300px;
        }

        /* Data Table */
        .table-section {
            background: var(--secondary-bg);
            border-radius: 15px;
            border: 1px solid var(--border-color);
            overflow: hidden;
        }

        .table-header {
            padding: 2rem;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .search-box {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .search-input {
            padding: 0.5rem 1rem;
            background: var(--primary-bg);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            color: var(--text-primary);
            width: 250px;
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
        }

        .data-table th,
        .data-table td {
            padding: 1rem 2rem;
            text-align: left;
            border-bottom: 1px solid var(--border-color);
        }

        .data-table th {
            background: var(--primary-bg);
            font-weight: 600;
            color: var(--text-secondary);
        }

        .data-table tbody tr:hover {
            background: rgba(0, 123, 255, 0.05);
        }

        .status-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
        }

        .status-active { background: rgba(40, 167, 69, 0.2); color: var(--success-color); }
        .status-inactive { background: rgba(220, 53, 69, 0.2); color: var(--danger-color); }
        .status-pending { background: rgba(255, 193, 7, 0.2); color: var(--warning-color); }

        .back-to-portfolio {
            position: fixed;
            top: 20px;
            right: 20px;
            background: var(--accent-color);
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            text-decoration: none;
            font-weight: 500;
            transition: var(--transition);
            z-index: 1001;
        }

        .back-to-portfolio:hover {
            background: var(--accent-hover);
            transform: translateY(-2px);
        }

        /* Mobile Responsive */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
            }

            .sidebar.active {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0;
                padding: 1rem;
            }

            .stats-grid {
                grid-template-columns: 1fr;
            }

            .charts-section {
                grid-template-columns: 1fr;
            }

            .header {
                flex-direction: column;
                gap: 1rem;
                align-items: flex-start;
            }

            .data-table {
                font-size: 0.9rem;
            }

            .data-table th,
            .data-table td {
                padding: 0.5rem 1rem;
            }
        }
    </style>
</head>
<body>
    <a href="../index.html" class="back-to-portfolio">
        <i class="fas fa-arrow-left"></i> Về Portfolio
    </a>

    <div class="dashboard">
        <!-- Sidebar -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <div class="logo">
                    <i class="fas fa-heartbeat"></i>
                    <span>Health AI</span>
                </div>
            </div>
            <nav class="sidebar-nav">
                <a href="#" class="nav-item active">
                    <i class="fas fa-tachometer-alt"></i>
                    <span>Dashboard</span>
                </a>
                <a href="#" class="nav-item">
                    <i class="fas fa-users"></i>
                    <span>Người dùng</span>
                </a>
                <a href="#" class="nav-item">
                    <i class="fas fa-chart-bar"></i>
                    <span>Thống kê</span>
                </a>
                <a href="#" class="nav-item">
                    <i class="fas fa-stethoscope"></i>
                    <span>Chẩn đoán</span>
                </a>
                <a href="#" class="nav-item">
                    <i class="fas fa-calendar-alt"></i>
                    <span>Lịch hẹn</span>
                </a>
                <a href="#" class="nav-item">
                    <i class="fas fa-cog"></i>
                    <span>Cài đặt</span>
                </a>
            </nav>
        </aside>

        <!-- Main Content -->
        <main class="main-content">
            <header class="header">
                <h1>Dashboard Tổng quan</h1>
                <div class="header-actions">
                    <button class="btn btn-primary">
                        <i class="fas fa-plus"></i>
                        Thêm mới
                    </button>
                    <div class="user-profile">
                        <div class="avatar">AT</div>
                        <div>
                            <div style="font-weight: 600;">Admin</div>
                            <div style="font-size: 0.8rem; color: var(--text-secondary);">Le Anh Tu</div>
                        </div>
                    </div>
                </div>
            </header>

            <!-- Stats Cards -->
            <section class="stats-grid">
                <div class="stat-card">
                    <div class="stat-header">
                        <div class="stat-title">Tổng người dùng</div>
                        <div class="stat-icon users">
                            <i class="fas fa-users"></i>
                        </div>
                    </div>
                    <div class="stat-value">1,234</div>
                    <div class="stat-change positive">
                        <i class="fas fa-arrow-up"></i>
                        +12% so với tháng trước
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-header">
                        <div class="stat-title">Phiên hoạt động</div>
                        <div class="stat-icon sessions">
                            <i class="fas fa-chart-line"></i>
                        </div>
                    </div>
                    <div class="stat-value">89</div>
                    <div class="stat-change positive">
                        <i class="fas fa-arrow-up"></i>
                        +5% hôm nay
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-header">
                        <div class="stat-title">Chẩn đoán AI</div>
                        <div class="stat-icon revenue">
                            <i class="fas fa-brain"></i>
                        </div>
                    </div>
                    <div class="stat-value">456</div>
                    <div class="stat-change positive">
                        <i class="fas fa-arrow-up"></i>
                        +8% tuần này
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-header">
                        <div class="stat-title">Độ chính xác</div>
                        <div class="stat-icon growth">
                            <i class="fas fa-bullseye"></i>
                        </div>
                    </div>
                    <div class="stat-value">98.5%</div>
                    <div class="stat-change positive">
                        <i class="fas fa-arrow-up"></i>
                        +0.3% cải thiện
                    </div>
                </div>
            </section>

            <!-- Charts Section -->
            <section class="charts-section">
                <div class="chart-card">
                    <div class="chart-header">
                        <h3 class="chart-title">Người dùng theo thời gian</h3>
                        <select style="background: var(--primary-bg); color: var(--text-primary); border: 1px solid var(--border-color); border-radius: 5px; padding: 5px;">
                            <option>7 ngày qua</option>
                            <option>30 ngày qua</option>
                            <option>3 tháng qua</option>
                        </select>
                    </div>
                    <div class="chart-container">
                        <canvas id="userChart"></canvas>
                    </div>
                </div>

                <div class="chart-card">
                    <div class="chart-header">
                        <h3 class="chart-title">Phân bố chẩn đoán</h3>
                    </div>
                    <div class="chart-container">
                        <canvas id="diagnosisChart"></canvas>
                    </div>
                </div>
            </section>

            <!-- Data Table -->
            <section class="table-section">
                <div class="table-header">
                    <h3>Người dùng gần đây</h3>
                    <div class="search-box">
                        <input type="text" class="search-input" placeholder="Tìm kiếm người dùng...">
                        <button class="btn btn-primary">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>Tên</th>
                            <th>Email</th>
                            <th>Ngày đăng ký</th>
                            <th>Trạng thái</th>
                            <th>Chẩn đoán</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>Nguyễn Văn A</td>
                            <td><EMAIL></td>
                            <td>22/08/2025</td>
                            <td><span class="status-badge status-active">Hoạt động</span></td>
                            <td>3</td>
                        </tr>
                        <tr>
                            <td>Trần Thị B</td>
                            <td><EMAIL></td>
                            <td>21/08/2025</td>
                            <td><span class="status-badge status-active">Hoạt động</span></td>
                            <td>1</td>
                        </tr>
                        <tr>
                            <td>Lê Văn C</td>
                            <td><EMAIL></td>
                            <td>20/08/2025</td>
                            <td><span class="status-badge status-pending">Chờ xác nhận</span></td>
                            <td>0</td>
                        </tr>
                        <tr>
                            <td>Phạm Thị D</td>
                            <td><EMAIL></td>
                            <td>19/08/2025</td>
                            <td><span class="status-badge status-inactive">Không hoạt động</span></td>
                            <td>5</td>
                        </tr>
                        <tr>
                            <td>Hoàng Văn E</td>
                            <td><EMAIL></td>
                            <td>18/08/2025</td>
                            <td><span class="status-badge status-active">Hoạt động</span></td>
                            <td>2</td>
                        </tr>
                    </tbody>
                </table>
            </section>
        </main>
    </div>

    <script>
        // User Growth Chart
        const userCtx = document.getElementById('userChart').getContext('2d');
        new Chart(userCtx, {
            type: 'line',
            data: {
                labels: ['T2', 'T3', 'T4', 'T5', 'T6', 'T7', 'CN'],
                datasets: [{
                    label: 'Người dùng mới',
                    data: [12, 19, 15, 25, 22, 30, 28],
                    borderColor: '#007bff',
                    backgroundColor: 'rgba(0, 123, 255, 0.1)',
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        labels: {
                            color: '#f0f0f0'
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            color: '#b0b0b0'
                        },
                        grid: {
                            color: '#333'
                        }
                    },
                    x: {
                        ticks: {
                            color: '#b0b0b0'
                        },
                        grid: {
                            color: '#333'
                        }
                    }
                }
            }
        });

        // Diagnosis Distribution Chart
        const diagnosisCtx = document.getElementById('diagnosisChart').getContext('2d');
        new Chart(diagnosisCtx, {
            type: 'doughnut',
            data: {
                labels: ['Tim mạch', 'Hô hấp', 'Tiêu hóa', 'Thần kinh', 'Khác'],
                datasets: [{
                    data: [30, 25, 20, 15, 10],
                    backgroundColor: [
                        '#007bff',
                        '#28a745',
                        '#ffc107',
                        '#dc3545',
                        '#6c757d'
                    ]
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            color: '#f0f0f0',
                            padding: 20
                        }
                    }
                }
            }
        });

        // Interactive elements
        document.querySelectorAll('.nav-item').forEach(item => {
            item.addEventListener('click', (e) => {
                e.preventDefault();
                document.querySelectorAll('.nav-item').forEach(nav => nav.classList.remove('active'));
                item.classList.add('active');
            });
        });

        // Search functionality
        const searchInput = document.querySelector('.search-input');
        const tableRows = document.querySelectorAll('.data-table tbody tr');

        searchInput.addEventListener('input', (e) => {
            const searchTerm = e.target.value.toLowerCase();
            tableRows.forEach(row => {
                const text = row.textContent.toLowerCase();
                row.style.display = text.includes(searchTerm) ? '' : 'none';
            });
        });

        // Animate stats on load
        window.addEventListener('load', () => {
            const statValues = document.querySelectorAll('.stat-value');
            statValues.forEach(stat => {
                const finalValue = stat.textContent;
                let currentValue = 0;
                const increment = finalValue.replace(/[^\d]/g, '') / 50;
                
                const timer = setInterval(() => {
                    currentValue += increment;
                    if (currentValue >= finalValue.replace(/[^\d]/g, '')) {
                        stat.textContent = finalValue;
                        clearInterval(timer);
                    } else {
                        stat.textContent = Math.floor(currentValue) + (finalValue.includes('%') ? '%' : '');
                    }
                }, 30);
            });
        });
    </script>
</body>
</html>
