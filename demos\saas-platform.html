<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CloudSync - All-in-One Business Platform</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            --primary-color: #8b5cf6;
            --primary-dark: #7c3aed;
            --secondary-color: #06b6d4;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --danger-color: #ef4444;
            --text-primary: #1f2937;
            --text-secondary: #6b7280;
            --text-light: #9ca3af;
            --bg-primary: #ffffff;
            --bg-secondary: #f9fafb;
            --bg-dark: #111827;
            --border-color: #e5e7eb;
            --shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            --shadow-lg: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
            --gradient: linear-gradient(135deg, #8b5cf6 0%, #06b6d4 100%);
            --transition: all 0.3s ease;
        }

        body {
            font-family: 'Inter', sans-serif;
            line-height: 1.6;
            color: var(--text-primary);
            background: var(--bg-primary);
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        /* Navigation */
        .navbar {
            position: fixed;
            top: 0;
            width: 100%;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            z-index: 1000;
            padding: 1rem 0;
            border-bottom: 1px solid var(--border-color);
            transition: var(--transition);
        }

        .nav-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 1.5rem;
            font-weight: 800;
            color: var(--primary-color);
        }

        .logo i {
            font-size: 2rem;
        }

        .nav-menu {
            display: flex;
            gap: 2rem;
            list-style: none;
        }

        .nav-link {
            color: var(--text-primary);
            text-decoration: none;
            font-weight: 500;
            transition: var(--transition);
            position: relative;
        }

        .nav-link:hover {
            color: var(--primary-color);
        }

        .nav-actions {
            display: flex;
            gap: 1rem;
            align-items: center;
        }

        .btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 8px;
            font-size: 0.9rem;
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition);
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .btn-outline {
            background: transparent;
            color: var(--text-primary);
            border: 1px solid var(--border-color);
        }

        .btn-outline:hover {
            border-color: var(--primary-color);
            color: var(--primary-color);
        }

        .btn-primary {
            background: var(--gradient);
            color: white;
            border: none;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow);
        }

        /* Hero Section */
        .hero {
            padding: 120px 0 80px;
            background: var(--bg-secondary);
            position: relative;
            overflow: hidden;
        }

        .hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: radial-gradient(circle at 30% 20%, rgba(139, 92, 246, 0.1) 0%, transparent 50%),
                        radial-gradient(circle at 70% 80%, rgba(6, 182, 212, 0.1) 0%, transparent 50%);
        }

        .hero-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 4rem;
            align-items: center;
            position: relative;
            z-index: 2;
        }

        .hero-text {
            animation: fadeInUp 0.8s ease;
        }

        .hero-badge {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            background: rgba(139, 92, 246, 0.1);
            color: var(--primary-color);
            padding: 0.5rem 1rem;
            border-radius: 50px;
            font-size: 0.9rem;
            font-weight: 600;
            margin-bottom: 2rem;
        }

        .hero-title {
            font-size: 3.5rem;
            font-weight: 800;
            line-height: 1.1;
            margin-bottom: 1.5rem;
            color: var(--text-primary);
        }

        .hero-subtitle {
            font-size: 1.2rem;
            color: var(--text-secondary);
            margin-bottom: 3rem;
            line-height: 1.7;
        }

        .hero-buttons {
            display: flex;
            gap: 1rem;
            flex-wrap: wrap;
        }

        .btn-large {
            padding: 1rem 2rem;
            font-size: 1rem;
            border-radius: 12px;
        }

        .hero-visual {
            position: relative;
            animation: fadeInUp 0.8s ease 0.2s both;
        }

        .dashboard-preview {
            background: white;
            border-radius: 20px;
            padding: 2rem;
            box-shadow: var(--shadow-lg);
            border: 1px solid var(--border-color);
        }

        .preview-header {
            display: flex;
            justify-content: between;
            align-items: center;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid var(--border-color);
        }

        .preview-title {
            font-size: 1.2rem;
            font-weight: 700;
            color: var(--text-primary);
        }

        .preview-stats {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: var(--bg-secondary);
            padding: 1.5rem;
            border-radius: 12px;
            text-align: center;
        }

        .stat-number {
            font-size: 2rem;
            font-weight: 800;
            color: var(--primary-color);
            margin-bottom: 0.5rem;
        }

        .stat-label {
            font-size: 0.9rem;
            color: var(--text-secondary);
        }

        .preview-chart {
            height: 200px;
            background: var(--bg-secondary);
            border-radius: 12px;
            position: relative;
            overflow: hidden;
        }

        .chart-line {
            position: absolute;
            bottom: 20px;
            left: 20px;
            right: 20px;
            height: 2px;
            background: var(--gradient);
            border-radius: 1px;
        }

        .chart-line::before {
            content: '';
            position: absolute;
            top: -50px;
            left: 0;
            width: 100%;
            height: 50px;
            background: linear-gradient(to bottom, rgba(139, 92, 246, 0.2), transparent);
            border-radius: 25px 25px 0 0;
        }

        /* Features Section */
        .features {
            padding: 100px 0;
            background: var(--bg-primary);
        }

        .section-header {
            text-align: center;
            margin-bottom: 4rem;
        }

        .section-badge {
            display: inline-block;
            background: rgba(139, 92, 246, 0.1);
            color: var(--primary-color);
            padding: 0.5rem 1rem;
            border-radius: 50px;
            font-size: 0.9rem;
            font-weight: 600;
            margin-bottom: 1rem;
        }

        .section-title {
            font-size: 2.5rem;
            font-weight: 800;
            margin-bottom: 1rem;
            color: var(--text-primary);
        }

        .section-subtitle {
            font-size: 1.1rem;
            color: var(--text-secondary);
            max-width: 600px;
            margin: 0 auto;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 2rem;
        }

        .feature-card {
            background: white;
            padding: 3rem 2rem;
            border-radius: 20px;
            border: 1px solid var(--border-color);
            transition: var(--transition);
            text-align: center;
        }

        .feature-card:hover {
            transform: translateY(-10px);
            box-shadow: var(--shadow-lg);
            border-color: var(--primary-color);
        }

        .feature-icon {
            width: 80px;
            height: 80px;
            margin: 0 auto 2rem;
            background: var(--gradient);
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            color: white;
        }

        .feature-title {
            font-size: 1.3rem;
            font-weight: 700;
            margin-bottom: 1rem;
            color: var(--text-primary);
        }

        .feature-description {
            color: var(--text-secondary);
            line-height: 1.7;
        }

        /* Pricing Section */
        .pricing {
            padding: 100px 0;
            background: var(--bg-secondary);
        }

        .pricing-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            max-width: 1000px;
            margin: 0 auto;
        }

        .pricing-card {
            background: white;
            padding: 3rem 2rem;
            border-radius: 20px;
            border: 1px solid var(--border-color);
            text-align: center;
            position: relative;
            transition: var(--transition);
        }

        .pricing-card:hover {
            transform: translateY(-10px);
            box-shadow: var(--shadow-lg);
        }

        .pricing-card.featured {
            border-color: var(--primary-color);
            transform: scale(1.05);
        }

        .pricing-card.featured::before {
            content: 'Phổ biến nhất';
            position: absolute;
            top: -10px;
            left: 50%;
            transform: translateX(-50%);
            background: var(--gradient);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
        }

        .plan-name {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 1rem;
            color: var(--text-primary);
        }

        .plan-price {
            font-size: 3rem;
            font-weight: 800;
            color: var(--primary-color);
            margin-bottom: 0.5rem;
        }

        .plan-period {
            color: var(--text-secondary);
            margin-bottom: 2rem;
        }

        .plan-features {
            list-style: none;
            margin-bottom: 3rem;
        }

        .plan-features li {
            padding: 0.5rem 0;
            color: var(--text-secondary);
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .plan-features li i {
            color: var(--success-color);
        }

        .back-to-portfolio {
            position: fixed;
            top: 20px;
            right: 20px;
            background: var(--primary-color);
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            text-decoration: none;
            font-weight: 500;
            transition: var(--transition);
            z-index: 1001;
        }

        .back-to-portfolio:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }

        /* Footer */
        .footer {
            background: var(--bg-dark);
            color: white;
            padding: 3rem 0;
            text-align: center;
        }

        /* Animations */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .animate-on-scroll {
            opacity: 0;
            transform: translateY(30px);
            transition: all 0.8s ease;
        }

        .animate-on-scroll.animated {
            opacity: 1;
            transform: translateY(0);
        }

        /* Mobile Responsive */
        @media (max-width: 768px) {
            .nav-menu {
                display: none;
            }

            .hero-content {
                grid-template-columns: 1fr;
                text-align: center;
            }

            .hero-title {
                font-size: 2.5rem;
            }

            .section-title {
                font-size: 2rem;
            }

            .features-grid {
                grid-template-columns: 1fr;
            }

            .pricing-grid {
                grid-template-columns: 1fr;
            }

            .pricing-card.featured {
                transform: none;
            }

            .preview-stats {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <a href="../index.html" class="back-to-portfolio">
        <i class="fas fa-arrow-left"></i> Về Portfolio
    </a>

    <!-- Navigation -->
    <nav class="navbar">
        <div class="container">
            <div class="nav-container">
                <div class="logo">
                    <i class="fas fa-cloud"></i>
                    CloudSync
                </div>
                <ul class="nav-menu">
                    <li><a href="#home" class="nav-link">Trang chủ</a></li>
                    <li><a href="#features" class="nav-link">Tính năng</a></li>
                    <li><a href="#pricing" class="nav-link">Bảng giá</a></li>
                    <li><a href="#contact" class="nav-link">Liên hệ</a></li>
                </ul>
                <div class="nav-actions">
                    <a href="#" class="btn btn-outline">Đăng nhập</a>
                    <a href="#" class="btn btn-primary">Dùng thử miễn phí</a>
                </div>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section id="home" class="hero">
        <div class="container">
            <div class="hero-content">
                <div class="hero-text">
                    <div class="hero-badge">
                        <i class="fas fa-rocket"></i>
                        Nền tảng SaaS hàng đầu 2025
                    </div>
                    <h1 class="hero-title">
                        Quản lý doanh nghiệp<br>
                        thông minh và hiệu quả
                    </h1>
                    <p class="hero-subtitle">
                        CloudSync giúp doanh nghiệp của bạn tối ưu hóa quy trình làm việc,
                        quản lý dự án và tăng năng suất với các công cụ AI tiên tiến.
                    </p>
                    <div class="hero-buttons">
                        <a href="#" class="btn btn-primary btn-large">
                            <i class="fas fa-play"></i>
                            Dùng thử miễn phí 14 ngày
                        </a>
                        <a href="#features" class="btn btn-outline btn-large">
                            <i class="fas fa-info-circle"></i>
                            Xem demo
                        </a>
                    </div>
                </div>
                <div class="hero-visual">
                    <div class="dashboard-preview">
                        <div class="preview-header">
                            <div class="preview-title">Dashboard Overview</div>
                        </div>
                        <div class="preview-stats">
                            <div class="stat-card">
                                <div class="stat-number">2.4K</div>
                                <div class="stat-label">Dự án</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number">89%</div>
                                <div class="stat-label">Hiệu suất</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number">156</div>
                                <div class="stat-label">Thành viên</div>
                            </div>
                        </div>
                        <div class="preview-chart">
                            <div class="chart-line"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section id="features" class="features">
        <div class="container">
            <div class="section-header animate-on-scroll">
                <div class="section-badge">Tính năng nổi bật</div>
                <h2 class="section-title">Tại sao chọn CloudSync?</h2>
                <p class="section-subtitle">
                    Chúng tôi cung cấp bộ công cụ toàn diện để giúp doanh nghiệp của bạn
                    phát triển mạnh mẽ trong thời đại số.
                </p>
            </div>
            <div class="features-grid">
                <div class="feature-card animate-on-scroll">
                    <div class="feature-icon">
                        <i class="fas fa-brain"></i>
                    </div>
                    <h3 class="feature-title">AI-Powered Analytics</h3>
                    <p class="feature-description">
                        Phân tích dữ liệu thông minh với AI, giúp bạn đưa ra quyết định
                        kinh doanh chính xác và tối ưu hóa hiệu suất.
                    </p>
                </div>
                <div class="feature-card animate-on-scroll">
                    <div class="feature-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <h3 class="feature-title">Team Collaboration</h3>
                    <p class="feature-description">
                        Công cụ cộng tác nhóm mạnh mẽ, giúp team làm việc hiệu quả
                        từ xa với chat, video call và chia sẻ file.
                    </p>
                </div>
                <div class="feature-card animate-on-scroll">
                    <div class="feature-icon">
                        <i class="fas fa-tasks"></i>
                    </div>
                    <h3 class="feature-title">Project Management</h3>
                    <p class="feature-description">
                        Quản lý dự án chuyên nghiệp với timeline, milestone,
                        task assignment và theo dõi tiến độ real-time.
                    </p>
                </div>
                <div class="feature-card animate-on-scroll">
                    <div class="feature-icon">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <h3 class="feature-title">Advanced Reporting</h3>
                    <p class="feature-description">
                        Báo cáo chi tiết với biểu đồ trực quan, KPI tracking
                        và insights để tối ưu hóa hiệu suất kinh doanh.
                    </p>
                </div>
                <div class="feature-card animate-on-scroll">
                    <div class="feature-icon">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <h3 class="feature-title">Enterprise Security</h3>
                    <p class="feature-description">
                        Bảo mật cấp doanh nghiệp với mã hóa end-to-end,
                        2FA, SSO và tuân thủ các tiêu chuẩn bảo mật quốc tế.
                    </p>
                </div>
                <div class="feature-card animate-on-scroll">
                    <div class="feature-icon">
                        <i class="fas fa-plug"></i>
                    </div>
                    <h3 class="feature-title">API Integration</h3>
                    <p class="feature-description">
                        Tích hợp dễ dàng với 500+ ứng dụng phổ biến
                        qua API mạnh mẽ và webhook tự động.
                    </p>
                </div>
            </div>
        </div>
    </section>

    <!-- Pricing Section -->
    <section id="pricing" class="pricing">
        <div class="container">
            <div class="section-header animate-on-scroll">
                <div class="section-badge">Bảng giá</div>
                <h2 class="section-title">Chọn gói phù hợp với bạn</h2>
                <p class="section-subtitle">
                    Linh hoạt từ startup đến doanh nghiệp lớn.
                    Bắt đầu miễn phí, nâng cấp khi cần thiết.
                </p>
            </div>
            <div class="pricing-grid">
                <div class="pricing-card animate-on-scroll">
                    <div class="plan-name">Starter</div>
                    <div class="plan-price">$0</div>
                    <div class="plan-period">Miễn phí mãi mãi</div>
                    <ul class="plan-features">
                        <li><i class="fas fa-check"></i> 5 dự án</li>
                        <li><i class="fas fa-check"></i> 10 thành viên</li>
                        <li><i class="fas fa-check"></i> 1GB lưu trữ</li>
                        <li><i class="fas fa-check"></i> Hỗ trợ email</li>
                        <li><i class="fas fa-check"></i> Báo cáo cơ bản</li>
                    </ul>
                    <a href="#" class="btn btn-outline" style="width: 100%;">Bắt đầu miễn phí</a>
                </div>

                <div class="pricing-card featured animate-on-scroll">
                    <div class="plan-name">Professional</div>
                    <div class="plan-price">$29</div>
                    <div class="plan-period">mỗi tháng</div>
                    <ul class="plan-features">
                        <li><i class="fas fa-check"></i> Dự án không giới hạn</li>
                        <li><i class="fas fa-check"></i> 100 thành viên</li>
                        <li><i class="fas fa-check"></i> 100GB lưu trữ</li>
                        <li><i class="fas fa-check"></i> Hỗ trợ 24/7</li>
                        <li><i class="fas fa-check"></i> AI Analytics</li>
                        <li><i class="fas fa-check"></i> API Access</li>
                    </ul>
                    <a href="#" class="btn btn-primary" style="width: 100%;">Chọn gói này</a>
                </div>

                <div class="pricing-card animate-on-scroll">
                    <div class="plan-name">Enterprise</div>
                    <div class="plan-price">$99</div>
                    <div class="plan-period">mỗi tháng</div>
                    <ul class="plan-features">
                        <li><i class="fas fa-check"></i> Tất cả tính năng Pro</li>
                        <li><i class="fas fa-check"></i> Thành viên không giới hạn</li>
                        <li><i class="fas fa-check"></i> 1TB lưu trữ</li>
                        <li><i class="fas fa-check"></i> Dedicated support</li>
                        <li><i class="fas fa-check"></i> Custom integrations</li>
                        <li><i class="fas fa-check"></i> Advanced security</li>
                    </ul>
                    <a href="#" class="btn btn-outline" style="width: 100%;">Liên hệ tư vấn</a>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <p>&copy; 2025 CloudSync. Thiết kế bởi Le Anh Tu - Front-end Developer</p>
        </div>
    </footer>

    <script>
        // Smooth scrolling
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Scroll animations
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animated');
                }
            });
        }, observerOptions);

        document.querySelectorAll('.animate-on-scroll').forEach(el => {
            observer.observe(el);
        });

        // Animate dashboard stats
        function animateStats() {
            const stats = document.querySelectorAll('.stat-number');
            stats.forEach(stat => {
                const finalValue = stat.textContent;
                const isPercentage = finalValue.includes('%');
                const numericValue = parseInt(finalValue.replace(/[^\d]/g, ''));
                let currentValue = 0;
                const increment = numericValue / 50;

                const timer = setInterval(() => {
                    currentValue += increment;
                    if (currentValue >= numericValue) {
                        stat.textContent = finalValue;
                        clearInterval(timer);
                    } else {
                        const displayValue = Math.floor(currentValue);
                        stat.textContent = isPercentage ? displayValue + '%' :
                                         displayValue > 1000 ? (displayValue / 1000).toFixed(1) + 'K' : displayValue;
                    }
                }, 30);
            });
        }

        // Trigger stats animation when hero is visible
        const heroObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    setTimeout(animateStats, 500);
                    heroObserver.unobserve(entry.target);
                }
            });
        }, { threshold: 0.5 });

        const heroSection = document.querySelector('.hero');
        if (heroSection) {
            heroObserver.observe(heroSection);
        }

        // Navbar background on scroll
        window.addEventListener('scroll', () => {
            const navbar = document.querySelector('.navbar');
            if (window.scrollY > 100) {
                navbar.style.background = 'rgba(255, 255, 255, 0.98)';
                navbar.style.boxShadow = '0 2px 20px rgba(0, 0, 0, 0.1)';
            } else {
                navbar.style.background = 'rgba(255, 255, 255, 0.95)';
                navbar.style.boxShadow = 'none';
            }
        });

        // Interactive pricing cards
        document.querySelectorAll('.pricing-card').forEach(card => {
            card.addEventListener('mouseenter', () => {
                if (!card.classList.contains('featured')) {
                    card.style.borderColor = 'var(--primary-color)';
                }
            });

            card.addEventListener('mouseleave', () => {
                if (!card.classList.contains('featured')) {
                    card.style.borderColor = 'var(--border-color)';
                }
            });
        });
    </script>
</body>
</html>
